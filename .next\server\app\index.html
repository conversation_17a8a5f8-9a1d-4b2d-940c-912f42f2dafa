<!DOCTYPE html><html lang="en" class="dark"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1"/><link rel="stylesheet" href="/_next/static/css/6616499dc2e5d3fd.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-f2112cdae129ae95.js"/><script src="/_next/static/chunks/4bd1b696-c83f4a4c9479adfb.js" async=""></script><script src="/_next/static/chunks/517-e167562a22416de6.js" async=""></script><script src="/_next/static/chunks/main-app-05d2147e03c41474.js" async=""></script><script src="/_next/static/chunks/app/page-410c20ced261f665.js" async=""></script><title>BlocksConnect - Connecting the Future</title><meta name="description" content="BlocksConnect is building the decentralized future, one block at a time. Experience the next generation of blockchain connectivity."/><meta name="author" content="BlocksConnect Team"/><meta name="keywords" content="blockchain,decentralized,web3,crypto,blocks,connect"/><meta name="creator" content="BlocksConnect"/><meta name="publisher" content="BlocksConnect"/><meta name="robots" content="index, follow"/><meta property="og:title" content="BlocksConnect - Connecting the Future"/><meta property="og:description" content="BlocksConnect is building the decentralized future, one block at a time."/><meta property="og:url" content="https://blocksconnect.com"/><meta property="og:site_name" content="BlocksConnect"/><meta property="og:locale" content="en_US"/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:creator" content="@blocksconnect"/><meta name="twitter:title" content="BlocksConnect - Connecting the Future"/><meta name="twitter:description" content="BlocksConnect is building the decentralized future, one block at a time."/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__variable_d65c78 font-inter-tight antialiased bg-black text-white"><div class="min-h-screen gradient-bg relative overflow-hidden"><canvas class="absolute inset-0 pointer-events-none opacity-30" style="z-index:1"></canvas><div class="absolute inset-0 opacity-10"><div class="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500 rounded-full blur-3xl animate-pulse-slow"></div><div class="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500 rounded-full blur-3xl animate-pulse-slow" style="animation-delay:1.5s"></div></div><main class="relative z-10 flex flex-col items-center justify-center min-h-screen px-4 sm:px-6 lg:px-8"><div class="text-center max-w-4xl mx-auto"><h1 class="font-inter-tight font-black text-6xl sm:text-7xl md:text-8xl lg:text-9xl xl:text-[10rem] leading-none tracking-tighter gradient-text mb-8 transition-all duration-1000 opacity-0">BlocksConnect</h1><p class="font-inter-tight font-light text-lg sm:text-xl md:text-2xl text-gray-300 mb-12 max-w-2xl mx-auto transition-all duration-1000 delay-300 opacity-0">Connecting the future, one block at a time</p><div class="flex flex-col sm:flex-row gap-4 justify-center items-center transition-all duration-1000 delay-500 opacity-0"><button class="group relative px-8 py-4 bg-white text-black font-inter-tight font-semibold text-lg rounded-lg hover-glow transition-all duration-300 hover:scale-105"><span class="relative z-10">Get Started</span><div class="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div></button><button class="group px-8 py-4 border border-gray-600 text-white font-inter-tight font-semibold text-lg rounded-lg hover:border-gray-400 transition-all duration-300 hover:scale-105 hover:bg-white/5">Learn More</button></div></div><div class="absolute top-20 left-10 w-4 h-4 bg-blue-400 rounded-full animate-float opacity-60"></div><div class="absolute top-40 right-20 w-6 h-6 bg-purple-400 rounded-full animate-float opacity-40" style="animation-delay:2s"></div><div class="absolute bottom-40 left-20 w-3 h-3 bg-green-400 rounded-full animate-float opacity-50" style="animation-delay:4s"></div><div class="absolute bottom-20 right-10 w-5 h-5 bg-yellow-400 rounded-full animate-float opacity-30" style="animation-delay:3s"></div></main><footer class="absolute bottom-0 left-0 right-0 p-6 text-center transition-all duration-1000 delay-700 opacity-0"><p class="font-inter-tight text-sm text-gray-500">© 2024 BlocksConnect. Building the decentralized future.</p></footer></div><script src="/_next/static/chunks/webpack-f2112cdae129ae95.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[5244,[],\"\"]\n3:I[3866,[],\"\"]\n4:I[7033,[],\"ClientPageRoot\"]\n5:I[1347,[\"974\",\"static/chunks/app/page-410c20ced261f665.js\"],\"default\"]\n8:I[6213,[],\"OutletBoundary\"]\na:I[6213,[],\"MetadataBoundary\"]\nc:I[6213,[],\"ViewportBoundary\"]\ne:I[4835,[],\"\"]\n:HL[\"/_next/static/css/6616499dc2e5d3fd.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"78x0ngsMiLlmjJPzD0D8e\",\"p\":\"\",\"c\":[\"\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/6616499dc2e5d3fd.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"className\":\"dark\",\"children\":[\"$\",\"body\",null,{\"className\":\"__variable_d65c78 font-inter-tight antialiased bg-black text-white\",\"children\":[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[],[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L4\",null,{\"Component\":\"$5\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@6\",\"$@7\"]}],null,[\"$\",\"$L8\",null,{\"children\":\"$L9\"}]]}],{},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"gLTyf0QdvMaRvDep_Jx7D\",{\"children\":[[\"$\",\"$La\",null,{\"children\":\"$Lb\"}],[\"$\",\"$Lc\",null,{\"children\":\"$Ld\"}],null]}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$e\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"6:{}\n7:{}\n"])</script><script>self.__next_f.push([1,"d:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1, maximum-scale=1\"}]]\nb:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"1\",{\"children\":\"BlocksConnect - Connecting the Future\"}],[\"$\",\"meta\",\"2\",{\"name\":\"description\",\"content\":\"BlocksConnect is building the decentralized future, one block at a time. Experience the next generation of blockchain connectivity.\"}],[\"$\",\"meta\",\"3\",{\"name\":\"author\",\"content\":\"BlocksConnect Team\"}],[\"$\",\"meta\",\"4\",{\"name\":\"keywords\",\"content\":\"blockchain,decentralized,web3,crypto,blocks,connect\"}],[\"$\",\"meta\",\"5\",{\"name\":\"creator\",\"content\":\"BlocksConnect\"}],[\"$\",\"meta\",\"6\",{\"name\":\"publisher\",\"content\":\"BlocksConnect\"}],[\"$\",\"meta\",\"7\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"meta\",\"8\",{\"property\":\"og:title\",\"content\":\"BlocksConnect - Connecting the Future\"}],[\"$\",\"meta\",\"9\",{\"property\":\"og:description\",\"content\":\"BlocksConnect is building the decentralized future, one block at a time.\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:url\",\"content\":\"https://blocksconnect.com\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:site_name\",\"content\":\"BlocksConnect\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:locale\",\"content\":\"en_US\"}],[\"$\",\"meta\",\"13\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"14\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"15\",{\"name\":\"twitter:creator\",\"content\":\"@blocksconnect\"}],[\"$\",\"meta\",\"16\",{\"name\":\"twitter:title\",\"content\":\"BlocksConnect - Connecting the Future\"}],[\"$\",\"meta\",\"17\",{\"name\":\"twitter:description\",\"content\":\"BlocksConnect is building the decentralized future, one block at a time.\"}],[\"$\",\"link\",\"18\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}]]\n"])</script><script>self.__next_f.push([1,"9:null\n"])</script></body></html>