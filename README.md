# BlocksConnect Landing Page

A modern, dark-themed landing page for BlocksConnect built with Next.js 15, React 19, TypeScript, and Tailwind CSS.

## 🚀 Features

- **Modern Dark Theme**: Sleek dark color scheme with gradient backgrounds
- **Typography**: Inter Tight font family for a modern, condensed look
- **Smooth Animations**:
  - Text reveal/fade-in effects
  - Floating elements
  - Hover effects with glow
  - Animated particle background
- **Responsive Design**: Mobile-first approach that works on all devices
- **Performance Optimized**: Fast loading with optimized fonts and animations
- **Accessibility**: Semantic HTML structure and proper contrast ratios

## 🛠️ Tech Stack

- **Framework**: Next.js 15 with App Router
- **React**: React 19
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Fonts**: Inter Tight (Google Fonts)
- **Animations**: Custom CSS animations and keyframes

## 🎨 Design Elements

- **Color Scheme**: Deep dark backgrounds (#0a0a0a) with light text
- **Typography**: Inter Tight font family with various weights (300-900)
- **Animations**:
  - Fade-in-up animations for content reveal
  - Floating particle background with connections
  - Pulse animations for background elements
  - Hover effects with scale and glow
- **Layout**: Centered hero section with minimal, focused design

## 🚀 Getting Started

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Run the development server**:
   ```bash
   npm run dev
   ```

3. **Open your browser**:
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
src/
├── app/
│   ├── globals.css          # Global styles and animations
│   ├── layout.tsx           # Root layout with metadata
│   └── page.tsx             # Main landing page
├── components/
│   └── AnimatedBackground.tsx # Particle animation background
```

## 🎯 Key Components

### Main Landing Page (`src/app/page.tsx`)
- Hero section with large "BlocksConnect" heading
- Subtitle and call-to-action buttons
- Floating decorative elements
- Responsive design with mobile-first approach

### Animated Background (`src/components/AnimatedBackground.tsx`)
- Canvas-based particle system
- Animated connections between particles
- Performance-optimized with requestAnimationFrame
- Responsive to window resizing

### Global Styles (`src/app/globals.css`)
- Custom CSS animations and keyframes
- Inter Tight font import
- Dark theme color variables
- Utility classes for animations

## 🎨 Customization

### Colors
Update the CSS variables in `globals.css`:
```css
:root {
  --background: #0a0a0a;
  --foreground: #ffffff;
  --accent: #3b82f6;
}
```

### Typography
Modify the font in `layout.tsx` and `tailwind.config.ts`:
```typescript
const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: 'swap',
});
```

### Animations
Customize animations in `globals.css` and `tailwind.config.ts`:
```css
@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}
```

## 📱 Responsive Breakpoints

- **Mobile**: < 640px
- **Tablet**: 640px - 1024px
- **Desktop**: > 1024px

## 🚀 Deployment

The easiest way to deploy is using [Vercel](https://vercel.com):

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy automatically

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.
