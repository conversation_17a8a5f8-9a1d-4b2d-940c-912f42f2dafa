# BlocksConnect Landing Page

A modern, dark-themed landing page for BlocksConnect built with Next.js 15, React 19, TypeScript, and Tailwind CSS.

## 🚀 Features

- **Modern Dark Theme**: Sleek dark color scheme with gradient backgrounds
- **Typography**: Inter Tight font family for a modern, condensed look
- **Smooth Animations**:
  - Text reveal/fade-in effects
  - Floating elements
  - Hover effects with glow
  - Animated particle background
- **Responsive Design**: Mobile-first approach that works on all devices
- **Performance Optimized**: Fast loading with optimized fonts and animations
- **Accessibility**: Semantic HTML structure and proper contrast ratios

## 🛠️ Tech Stack

- **Framework**: Next.js 15 with App Router
- **React**: React 19
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Fonts**: Inter Tight (Google Fonts)
- **Animations**: Custom CSS animations and keyframes

## 🎨 Design Elements

- **Color Scheme**: Deep dark backgrounds (#0a0a0a) with light text
- **Typography**: Inter Tight font family with various weights (300-900)
- **Animations**:
  - Fade-in-up animations for content reveal
  - Floating particle background with connections
  - Pulse animations for background elements
  - Hover effects with scale and glow
- **Layout**: Centered hero section with minimal, focused design

## 🚀 Getting Started

### Local Development

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Run the development server**:
   ```bash
   npm run dev
   ```

3. **Open your browser**:
   Navigate to [http://localhost:3000](http://localhost:3000)

### Docker Development

1. **Run with Docker Compose (Development)**:
   ```bash
   docker-compose -f docker-compose.dev.yml up --build
   ```

2. **Access the application**:
   Navigate to [http://localhost:3000](http://localhost:3000)

### Docker Production

1. **Build and run production container**:
   ```bash
   docker-compose up --build -d
   ```

2. **Access the application**:
   Navigate to [http://localhost:3000](http://localhost:3000)

3. **Stop the container**:
   ```bash
   docker-compose down
   ```

## 📁 Project Structure

```
src/
├── app/
│   ├── globals.css          # Global styles and animations
│   ├── layout.tsx           # Root layout with metadata
│   └── page.tsx             # Main landing page
├── components/
│   └── AnimatedBackground.tsx # Particle animation background
```

## 🎯 Key Components

### Main Landing Page (`src/app/page.tsx`)
- Hero section with large "BlocksConnect" heading
- Subtitle and call-to-action buttons
- Floating decorative elements
- Responsive design with mobile-first approach

### Animated Background (`src/components/AnimatedBackground.tsx`)
- Canvas-based particle system
- Animated connections between particles
- Performance-optimized with requestAnimationFrame
- Responsive to window resizing

### Global Styles (`src/app/globals.css`)
- Custom CSS animations and keyframes
- Inter Tight font import
- Dark theme color variables
- Utility classes for animations

## 🎨 Customization

### Colors
Update the CSS variables in `globals.css`:
```css
:root {
  --background: #0a0a0a;
  --foreground: #ffffff;
  --accent: #3b82f6;
}
```

### Typography
Modify the font in `layout.tsx` and `tailwind.config.ts`:
```typescript
const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: 'swap',
});
```

### Animations
Customize animations in `globals.css` and `tailwind.config.ts`:
```css
@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}
```

## 📱 Responsive Breakpoints

- **Mobile**: < 640px
- **Tablet**: 640px - 1024px
- **Desktop**: > 1024px

## 🐳 Docker

### Docker Files

- **`Dockerfile`**: Production-optimized multi-stage build
- **`Dockerfile.dev`**: Development container with hot reload
- **`docker-compose.yml`**: Production deployment
- **`docker-compose.dev.yml`**: Development environment
- **`.dockerignore`**: Optimized build context

### Docker Commands

```bash
# Development
docker-compose -f docker-compose.dev.yml up --build

# Production
docker-compose up --build -d

# Stop containers
docker-compose down

# View logs
docker-compose logs -f

# Rebuild without cache
docker-compose build --no-cache
```

### Error Pages

The application includes custom error pages that match the BlocksConnect design:

- **404 Not Found** (`/not-found`): Custom "Block Not Found" page
- **Error Boundary** (`/error`): Application error handling
- **Global Error** (`/global-error`): Critical system errors

All error pages feature:
- Same dark theme and animations as main page
- Blockchain-themed error messages
- Navigation back to home page
- Responsive design

## 🚀 Deployment

### Vercel (Recommended)

The easiest way to deploy is using [Vercel](https://vercel.com):

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy automatically

### Docker Deployment

For containerized deployment:

1. **Build production image**:
   ```bash
   docker build -t blocksconnect-landing .
   ```

2. **Run container**:
   ```bash
   docker run -p 3000:3000 blocksconnect-landing
   ```

3. **Or use Docker Compose**:
   ```bash
   docker-compose up -d
   ```

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.
