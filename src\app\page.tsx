'use client';

import { useEffect, useState } from 'react';
import AnimatedBackground from '@/components/AnimatedBackground';

export default function Home() {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  return (
    <div className="min-h-screen gradient-bg relative overflow-hidden">
      {/* Animated Background */}
      <AnimatedBackground />

      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500 rounded-full blur-3xl animate-pulse-slow"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500 rounded-full blur-3xl animate-pulse-slow" style={{ animationDelay: '1.5s' }}></div>
      </div>

      {/* Main Content */}
      <main className="relative z-10 flex flex-col items-center justify-center min-h-screen px-4 sm:px-6 lg:px-8">
        {/* Hero Section */}
        <div className="text-center max-w-4xl mx-auto">
          {/* Main Heading */}
          <h1
            className={`font-inter-tight font-black text-6xl sm:text-7xl md:text-8xl lg:text-9xl xl:text-[10rem] leading-none tracking-tighter gradient-text mb-8 transition-all duration-1000 ${
              isLoaded ? 'animate-fade-in-up opacity-100' : 'opacity-0'
            }`}
          >
            BlocksConnect
          </h1>

          {/* Subtitle */}
          <p
            className={`font-inter-tight font-light text-lg sm:text-xl md:text-2xl text-gray-300 mb-12 max-w-2xl mx-auto transition-all duration-1000 delay-300 ${
              isLoaded ? 'animate-fade-in-up opacity-100' : 'opacity-0'
            }`}
          >
            Connecting the future, one block at a time
          </p>

          {/* CTA Buttons */}
          <div
            className={`flex flex-col sm:flex-row gap-4 justify-center items-center transition-all duration-1000 delay-500 ${
              isLoaded ? 'animate-fade-in-up opacity-100' : 'opacity-0'
            }`}
          >
            <button className="group relative px-8 py-4 bg-white text-black font-inter-tight font-semibold text-lg rounded-lg hover-glow transition-all duration-300 hover:scale-105">
              <span className="relative z-10">Get Started</span>
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
            </button>

            <button className="group px-8 py-4 border border-gray-600 text-white font-inter-tight font-semibold text-lg rounded-lg hover:border-gray-400 transition-all duration-300 hover:scale-105 hover:bg-white/5">
              Learn More
            </button>
          </div>
        </div>

        {/* Floating Elements */}
        <div className="absolute top-20 left-10 w-4 h-4 bg-blue-400 rounded-full animate-float opacity-60"></div>
        <div className="absolute top-40 right-20 w-6 h-6 bg-purple-400 rounded-full animate-float opacity-40" style={{ animationDelay: '2s' }}></div>
        <div className="absolute bottom-40 left-20 w-3 h-3 bg-green-400 rounded-full animate-float opacity-50" style={{ animationDelay: '4s' }}></div>
        <div className="absolute bottom-20 right-10 w-5 h-5 bg-yellow-400 rounded-full animate-float opacity-30" style={{ animationDelay: '3s' }}></div>
      </main>

      {/* Footer */}
      <footer
        className={`absolute bottom-0 left-0 right-0 p-6 text-center transition-all duration-1000 delay-700 ${
          isLoaded ? 'animate-fade-in opacity-100' : 'opacity-0'
        }`}
      >
        <p className="font-inter-tight text-sm text-gray-500">
          © 2024 BlocksConnect. Building the decentralized future.
        </p>
      </footer>
    </div>
  );
}
