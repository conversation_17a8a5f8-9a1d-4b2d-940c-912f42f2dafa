version: '3.8'

services:
  blocksconnect-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: blocksconnect-landing
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
    restart: unless-stopped
    networks:
      - blocksconnect-network

networks:
  blocksconnect-network:
    driver: bridge

# Optional: Add volumes for persistent data if needed in the future
# volumes:
#   blocksconnect-data:
