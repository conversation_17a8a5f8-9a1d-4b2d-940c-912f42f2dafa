(()=>{var e={};e.id=492,e.ids=[492],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},306:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>s.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var r=n(260),o=n(8203),i=n(5155),s=n.n(i),a=n(7292),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);n.d(t,l);let c=["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.t.bind(n,9937,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(n.bind(n,1354)),"C:\\Users\\<USER>\\Documents\\Reactprojects\\landingpage-blocksconnect\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,9937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(n.t.bind(n,9116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(n.t.bind(n,1485,23)),"next/dist/client/components/unauthorized-error"]}],d=[],u={require:n,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},4026:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,3219,23)),Promise.resolve().then(n.t.bind(n,4863,23)),Promise.resolve().then(n.t.bind(n,5155,23)),Promise.resolve().then(n.t.bind(n,802,23)),Promise.resolve().then(n.t.bind(n,9350,23)),Promise.resolve().then(n.t.bind(n,8530,23)),Promise.resolve().then(n.t.bind(n,8921,23))},1954:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,6959,23)),Promise.resolve().then(n.t.bind(n,3875,23)),Promise.resolve().then(n.t.bind(n,8903,23)),Promise.resolve().then(n.t.bind(n,7174,23)),Promise.resolve().then(n.t.bind(n,4178,23)),Promise.resolve().then(n.t.bind(n,7190,23)),Promise.resolve().then(n.t.bind(n,1365,23))},9748:()=>{},6548:()=>{},1354:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>l,metadata:()=>s,viewport:()=>a});var r=n(2740),o=n(1194),i=n.n(o);n(1135);let s={title:"BlocksConnect - Connecting the Future",description:"BlocksConnect is building the decentralized future, one block at a time. Experience the next generation of blockchain connectivity.",keywords:["blockchain","decentralized","web3","crypto","blocks","connect"],authors:[{name:"BlocksConnect Team"}],creator:"BlocksConnect",publisher:"BlocksConnect",robots:{index:!0,follow:!0},openGraph:{type:"website",locale:"en_US",url:"https://blocksconnect.com",title:"BlocksConnect - Connecting the Future",description:"BlocksConnect is building the decentralized future, one block at a time.",siteName:"BlocksConnect"},twitter:{card:"summary_large_image",title:"BlocksConnect - Connecting the Future",description:"BlocksConnect is building the decentralized future, one block at a time.",creator:"@blocksconnect"}},a={width:"device-width",initialScale:1,maximumScale:1};function l({children:e}){return(0,r.jsx)("html",{lang:"en",className:"dark",children:(0,r.jsx)("body",{className:`${i().variable} font-inter-tight antialiased bg-black text-white`,children:e})})}},1135:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[638,104],()=>n(306));module.exports=r})();