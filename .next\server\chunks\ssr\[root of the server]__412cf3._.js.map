{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Reactprojects/landingpage-blocksconnect/src/components/AnimatedBackground.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\n\ninterface Particle {\n  x: number;\n  y: number;\n  vx: number;\n  vy: number;\n  size: number;\n  opacity: number;\n  color: string;\n}\n\nexport default function AnimatedBackground() {\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const particlesRef = useRef<Particle[]>([]);\n  const animationRef = useRef<number | null>(null);\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    const resizeCanvas = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n    };\n\n    const createParticles = () => {\n      const particles: Particle[] = [];\n      const particleCount = Math.min(50, Math.floor((canvas.width * canvas.height) / 15000));\n\n      for (let i = 0; i < particleCount; i++) {\n        particles.push({\n          x: Math.random() * canvas.width,\n          y: Math.random() * canvas.height,\n          vx: (Math.random() - 0.5) * 0.5,\n          vy: (Math.random() - 0.5) * 0.5,\n          size: Math.random() * 2 + 1,\n          opacity: Math.random() * 0.5 + 0.1,\n          color: ['#3b82f6', '#8b5cf6', '#06b6d4', '#10b981'][Math.floor(Math.random() * 4)]\n        });\n      }\n\n      particlesRef.current = particles;\n    };\n\n    const drawParticles = () => {\n      ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n      particlesRef.current.forEach((particle, index) => {\n        // Update position\n        particle.x += particle.vx;\n        particle.y += particle.vy;\n\n        // Wrap around edges\n        if (particle.x < 0) particle.x = canvas.width;\n        if (particle.x > canvas.width) particle.x = 0;\n        if (particle.y < 0) particle.y = canvas.height;\n        if (particle.y > canvas.height) particle.y = 0;\n\n        // Draw particle\n        ctx.beginPath();\n        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);\n        ctx.fillStyle = particle.color;\n        ctx.globalAlpha = particle.opacity;\n        ctx.fill();\n\n        // Draw connections\n        particlesRef.current.slice(index + 1).forEach(otherParticle => {\n          const dx = particle.x - otherParticle.x;\n          const dy = particle.y - otherParticle.y;\n          const distance = Math.sqrt(dx * dx + dy * dy);\n\n          if (distance < 100) {\n            ctx.beginPath();\n            ctx.moveTo(particle.x, particle.y);\n            ctx.lineTo(otherParticle.x, otherParticle.y);\n            ctx.strokeStyle = particle.color;\n            ctx.globalAlpha = (1 - distance / 100) * 0.2;\n            ctx.lineWidth = 0.5;\n            ctx.stroke();\n          }\n        });\n      });\n\n      ctx.globalAlpha = 1;\n    };\n\n    const animate = () => {\n      drawParticles();\n      animationRef.current = requestAnimationFrame(animate);\n    };\n\n    resizeCanvas();\n    createParticles();\n    animate();\n\n    const handleResize = () => {\n      resizeCanvas();\n      createParticles();\n    };\n\n    window.addEventListener('resize', handleResize);\n\n    return () => {\n      window.removeEventListener('resize', handleResize);\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n    };\n  }, []);\n\n  return (\n    <canvas\n      ref={canvasRef}\n      className=\"absolute inset-0 pointer-events-none opacity-30\"\n      style={{ zIndex: 1 }}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAce,SAAS;IACtB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc,EAAE;IAC1C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAiB;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,SAAS,UAAU,OAAO;QAChC,IAAI,CAAC,QAAQ;QAEb,MAAM,MAAM,OAAO,UAAU,CAAC;QAC9B,IAAI,CAAC,KAAK;QAEV,MAAM,eAAe;YACnB,OAAO,KAAK,GAAG,OAAO,UAAU;YAChC,OAAO,MAAM,GAAG,OAAO,WAAW;QACpC;QAEA,MAAM,kBAAkB;YACtB,MAAM,YAAwB,EAAE;YAChC,MAAM,gBAAgB,KAAK,GAAG,CAAC,IAAI,KAAK,KAAK,CAAC,AAAC,OAAO,KAAK,GAAG,OAAO,MAAM,GAAI;YAE/E,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;gBACtC,UAAU,IAAI,CAAC;oBACb,GAAG,KAAK,MAAM,KAAK,OAAO,KAAK;oBAC/B,GAAG,KAAK,MAAM,KAAK,OAAO,MAAM;oBAChC,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBAC5B,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBAC5B,MAAM,KAAK,MAAM,KAAK,IAAI;oBAC1B,SAAS,KAAK,MAAM,KAAK,MAAM;oBAC/B,OAAO;wBAAC;wBAAW;wBAAW;wBAAW;qBAAU,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,GAAG;gBACpF;YACF;YAEA,aAAa,OAAO,GAAG;QACzB;QAEA,MAAM,gBAAgB;YACpB,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;YAE/C,aAAa,OAAO,CAAC,OAAO,CAAC,CAAC,UAAU;gBACtC,kBAAkB;gBAClB,SAAS,CAAC,IAAI,SAAS,EAAE;gBACzB,SAAS,CAAC,IAAI,SAAS,EAAE;gBAEzB,oBAAoB;gBACpB,IAAI,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG,OAAO,KAAK;gBAC7C,IAAI,SAAS,CAAC,GAAG,OAAO,KAAK,EAAE,SAAS,CAAC,GAAG;gBAC5C,IAAI,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG,OAAO,MAAM;gBAC9C,IAAI,SAAS,CAAC,GAAG,OAAO,MAAM,EAAE,SAAS,CAAC,GAAG;gBAE7C,gBAAgB;gBAChB,IAAI,SAAS;gBACb,IAAI,GAAG,CAAC,SAAS,CAAC,EAAE,SAAS,CAAC,EAAE,SAAS,IAAI,EAAE,GAAG,KAAK,EAAE,GAAG;gBAC5D,IAAI,SAAS,GAAG,SAAS,KAAK;gBAC9B,IAAI,WAAW,GAAG,SAAS,OAAO;gBAClC,IAAI,IAAI;gBAER,mBAAmB;gBACnB,aAAa,OAAO,CAAC,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAA;oBAC5C,MAAM,KAAK,SAAS,CAAC,GAAG,cAAc,CAAC;oBACvC,MAAM,KAAK,SAAS,CAAC,GAAG,cAAc,CAAC;oBACvC,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK;oBAE1C,IAAI,WAAW,KAAK;wBAClB,IAAI,SAAS;wBACb,IAAI,MAAM,CAAC,SAAS,CAAC,EAAE,SAAS,CAAC;wBACjC,IAAI,MAAM,CAAC,cAAc,CAAC,EAAE,cAAc,CAAC;wBAC3C,IAAI,WAAW,GAAG,SAAS,KAAK;wBAChC,IAAI,WAAW,GAAG,CAAC,IAAI,WAAW,GAAG,IAAI;wBACzC,IAAI,SAAS,GAAG;wBAChB,IAAI,MAAM;oBACZ;gBACF;YACF;YAEA,IAAI,WAAW,GAAG;QACpB;QAEA,MAAM,UAAU;YACd;YACA,aAAa,OAAO,GAAG,sBAAsB;QAC/C;QAEA;QACA;QACA;QAEA,MAAM,eAAe;YACnB;YACA;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAElC,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;YACrC,IAAI,aAAa,OAAO,EAAE;gBACxB,qBAAqB,aAAa,OAAO;YAC3C;QACF;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;QACV,OAAO;YAAE,QAAQ;QAAE;;;;;;AAGzB"}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Reactprojects/landingpage-blocksconnect/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport AnimatedBackground from '@/components/AnimatedBackground';\n\nexport default function Home() {\n  const [isLoaded, setIsLoaded] = useState(false);\n\n  useEffect(() => {\n    setIsLoaded(true);\n  }, []);\n\n  return (\n    <div className=\"min-h-screen gradient-bg relative overflow-hidden\">\n      {/* Animated Background */}\n      <AnimatedBackground />\n\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500 rounded-full blur-3xl animate-pulse-slow\"></div>\n        <div className=\"absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500 rounded-full blur-3xl animate-pulse-slow\" style={{ animationDelay: '1.5s' }}></div>\n      </div>\n\n      {/* Main Content */}\n      <main className=\"relative z-10 flex flex-col items-center justify-center min-h-screen px-4 sm:px-6 lg:px-8\">\n        {/* Hero Section */}\n        <div className=\"text-center max-w-4xl mx-auto\">\n          {/* Main Heading */}\n          <h1\n            className={`font-inter-tight font-black text-6xl sm:text-7xl md:text-8xl lg:text-9xl xl:text-[10rem] leading-none tracking-tighter gradient-text mb-8 transition-all duration-1000 ${\n              isLoaded ? 'animate-fade-in-up opacity-100' : 'opacity-0'\n            }`}\n          >\n            BlocksConnect\n          </h1>\n\n        </div>\n\n        {/* Floating Elements */}\n        <div className=\"absolute top-20 left-10 w-4 h-4 bg-blue-400 rounded-full animate-float opacity-60\"></div>\n        <div className=\"absolute top-40 right-20 w-6 h-6 bg-purple-400 rounded-full animate-float opacity-40\" style={{ animationDelay: '2s' }}></div>\n        <div className=\"absolute bottom-40 left-20 w-3 h-3 bg-green-400 rounded-full animate-float opacity-50\" style={{ animationDelay: '4s' }}></div>\n        <div className=\"absolute bottom-20 right-10 w-5 h-5 bg-yellow-400 rounded-full animate-float opacity-30\" style={{ animationDelay: '3s' }}></div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,wIAAA,CAAA,UAAkB;;;;;0BAGnB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;wBAAiG,OAAO;4BAAE,gBAAgB;wBAAO;;;;;;;;;;;;0BAIlJ,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;kCAEb,cAAA,8OAAC;4BACC,WAAW,CAAC,uKAAuK,EACjL,WAAW,mCAAmC,aAC9C;sCACH;;;;;;;;;;;kCAOH,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;wBAAuF,OAAO;4BAAE,gBAAgB;wBAAK;;;;;;kCACpI,8OAAC;wBAAI,WAAU;wBAAwF,OAAO;4BAAE,gBAAgB;wBAAK;;;;;;kCACrI,8OAAC;wBAAI,WAAU;wBAA0F,OAAO;4BAAE,gBAAgB;wBAAK;;;;;;;;;;;;;;;;;;AAI/I"}}, {"offset": {"line": 246, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 256, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Reactprojects/landingpage-blocksconnect/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n    } else if (process.env.TURBOPACK) {\n      module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n    } else {\n      module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n    } else if (process.env.TURBOPACK) {\n      module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n    } else {\n      module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAQzC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1CJ,OAAOC,OAAO,GAAGC,QAAQ;QAC3B,OAAO,IAAIL,QAAQC,GAAG,CAACO,SAAS,EAAE;;QAIlC;IACF;AACF", "ignoreList": [0]}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 275, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Reactprojects/landingpage-blocksconnect/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,0HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0]}}, {"offset": {"line": 277, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 282, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Reactprojects/landingpage-blocksconnect/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,0HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0]}}, {"offset": {"line": 284, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}