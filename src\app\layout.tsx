import type { Metadata, Viewport } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: 'swap',
});

export const metadata: Metadata = {
  title: "BlocksConnect - Connecting the Future",
  description: "BlocksConnect is building the decentralized future, one block at a time. Experience the next generation of blockchain connectivity.",
  keywords: ["blockchain", "decentralized", "web3", "crypto", "blocks", "connect"],
  authors: [{ name: "BlocksConnect Team" }],
  creator: "BlocksConnect",
  publisher: "BlocksConnect",
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://blocksconnect.com",
    title: "BlocksConnect - Connecting the Future",
    description: "BlocksConnect is building the decentralized future, one block at a time.",
    siteName: "BlocksConnect",
  },
  twitter: {
    card: "summary_large_image",
    title: "BlocksConnect - Connecting the Future",
    description: "BlocksConnect is building the decentralized future, one block at a time.",
    creator: "@blocksconnect",
  },
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <body
        className={`${inter.variable} font-inter-tight antialiased bg-black text-white`}
      >
        {children}
      </body>
    </html>
  );
}
