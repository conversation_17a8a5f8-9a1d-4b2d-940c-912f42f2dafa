import { render } from '@testing-library/react';
import AnimatedBackground from '../AnimatedBackground';

// Mock canvas context
const mockGetContext = jest.fn(() => ({
  clearRect: jest.fn(),
  beginPath: jest.fn(),
  arc: jest.fn(),
  fill: jest.fn(),
  moveTo: jest.fn(),
  lineTo: jest.fn(),
  stroke: jest.fn(),
}));

// Mock HTMLCanvasElement
Object.defineProperty(HTMLCanvasElement.prototype, 'getContext', {
  value: mockGetContext,
});

// Mock requestAnimationFrame
global.requestAnimationFrame = jest.fn((cb) => {
  setTimeout(cb, 16);
  return 1;
});

global.cancelAnimationFrame = jest.fn();

describe('AnimatedBackground', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    render(<AnimatedBackground />);
  });

  it('creates a canvas element', () => {
    const { container } = render(<AnimatedBackground />);
    const canvas = container.querySelector('canvas');
    expect(canvas).toBeInTheDocument();
  });

  it('applies correct CSS classes', () => {
    const { container } = render(<AnimatedBackground />);
    const canvas = container.querySelector('canvas');
    expect(canvas).toHaveClass('absolute', 'inset-0', 'pointer-events-none', 'opacity-30');
  });
});
