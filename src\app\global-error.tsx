'use client';

import { useEffect, useState } from 'react';

interface GlobalErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function GlobalError({ error, reset }: GlobalErrorProps) {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
    // Log the error to an error reporting service
    console.error('Global application error:', error);
  }, [error]);

  return (
    <html lang="en" className="dark">
      <body className="font-inter-tight antialiased bg-black text-white">
        <div className="min-h-screen gradient-bg relative overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-red-500 rounded-full blur-3xl animate-pulse-slow"></div>
            <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500 rounded-full blur-3xl animate-pulse-slow" style={{ animationDelay: '1.5s' }}></div>
          </div>

          {/* Main Content */}
          <main className="relative z-10 flex flex-col items-center justify-center min-h-screen px-6 sm:px-8 lg:px-12">
            {/* Error Section */}
            <div className="text-center w-full max-w-7xl mx-auto">
              {/* Error Icon */}
              <div 
                className={`font-inter-tight font-black text-6xl sm:text-7xl md:text-8xl lg:text-9xl xl:text-[12rem] leading-none tracking-tighter text-white/20 mb-4 transition-all duration-1000 ${
                  isLoaded ? 'animate-fade-in-up opacity-100' : 'opacity-0'
                }`}
              >
                💥
              </div>

              {/* Main Heading */}
              <h1 
                className={`font-inter-tight font-black text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl leading-none tracking-tighter gradient-text mb-6 transition-all duration-1000 delay-300 ${
                  isLoaded ? 'animate-fade-in-up opacity-100' : 'opacity-0'
                }`}
              >
                System Failure
              </h1>

              {/* Subtitle */}
              <p 
                className={`font-inter-tight font-light text-lg sm:text-xl md:text-2xl text-gray-300 mb-8 max-w-2xl mx-auto transition-all duration-1000 delay-500 ${
                  isLoaded ? 'animate-fade-in-up opacity-100' : 'opacity-0'
                }`}
              >
                A critical error occurred in the BlocksConnect system. Please try refreshing the page.
              </p>

              {/* Action Buttons */}
              <div 
                className={`flex flex-col sm:flex-row gap-4 justify-center items-center transition-all duration-1000 delay-700 ${
                  isLoaded ? 'animate-fade-in-up opacity-100' : 'opacity-0'
                }`}
              >
                <button 
                  onClick={reset}
                  className="group relative px-8 py-4 bg-white text-black font-inter-tight font-semibold text-lg rounded-lg hover-glow transition-all duration-300 hover:scale-105"
                >
                  <span className="relative z-10">Restart System</span>
                  <div className="absolute inset-0 bg-gradient-to-r from-red-500 to-purple-600 rounded-lg opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                </button>
                
                <button 
                  onClick={() => window.location.href = '/'}
                  className="group px-8 py-4 border border-gray-600 text-white font-inter-tight font-semibold text-lg rounded-lg hover:border-gray-400 transition-all duration-300 hover:scale-105 hover:bg-white/5"
                >
                  Reload Page
                </button>
              </div>
            </div>

            {/* Floating Elements - Critical error colors */}
            <div className="absolute top-20 left-10 w-4 h-4 bg-red-400 rounded-full animate-float opacity-60"></div>
            <div className="absolute top-40 right-20 w-6 h-6 bg-purple-400 rounded-full animate-float opacity-40" style={{ animationDelay: '2s' }}></div>
            <div className="absolute bottom-40 left-20 w-3 h-3 bg-orange-400 rounded-full animate-float opacity-50" style={{ animationDelay: '4s' }}></div>
            <div className="absolute bottom-20 right-10 w-5 h-5 bg-pink-400 rounded-full animate-float opacity-30" style={{ animationDelay: '3s' }}></div>
          </main>
        </div>

        {/* Inline styles for animations since global CSS might not be loaded */}
        <style jsx global>{`
          @import url('https://fonts.googleapis.com/css2?family=Inter+Tight:wght@300;400;500;600;700;800;900&display=swap');
          
          .gradient-bg {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
          }
          
          .gradient-text {
            background: linear-gradient(135deg, #ffffff 0%, #a1a1aa 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
          }
          
          .hover-glow {
            transition: all 0.3s ease;
          }
          
          .hover-glow:hover {
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
            transform: translateY(-2px);
          }
          
          @keyframes fadeInUp {
            from {
              opacity: 0;
              transform: translateY(30px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }
          
          @keyframes float {
            0%, 100% {
              transform: translateY(0px);
            }
            50% {
              transform: translateY(-10px);
            }
          }
          
          .animate-fade-in-up {
            animation: fadeInUp 0.8s ease-out forwards;
          }
          
          .animate-float {
            animation: float 6s ease-in-out infinite;
          }
          
          .animate-pulse-slow {
            animation: pulse 3s ease-in-out infinite;
          }
        `}</style>
      </body>
    </html>
  );
}
