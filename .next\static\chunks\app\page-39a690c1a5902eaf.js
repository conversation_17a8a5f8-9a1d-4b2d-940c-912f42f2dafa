(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{5497:(e,t,a)=>{Promise.resolve().then(a.bind(a,1347))},1347:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});var l=a(5155),n=a(2115);function i(){let e=(0,n.useRef)(null),t=(0,n.useRef)([]),a=(0,n.useRef)(null);return(0,n.useEffect)(()=>{let l=e.current;if(!l)return;let n=l.getContext("2d");if(!n)return;let i=()=>{l.width=window.innerWidth,l.height=window.innerHeight},s=()=>{let e=[],a=Math.min(50,Math.floor(l.width*l.height/15e3));for(let t=0;t<a;t++)e.push({x:Math.random()*l.width,y:Math.random()*l.height,vx:(Math.random()-.5)*.5,vy:(Math.random()-.5)*.5,size:2*Math.random()+1,opacity:.5*Math.random()+.1,color:["#3b82f6","#8b5cf6","#06b6d4","#10b981"][Math.floor(4*Math.random())]});t.current=e},o=()=>{n.clearRect(0,0,l.width,l.height),t.current.forEach((e,a)=>{e.x+=e.vx,e.y+=e.vy,e.x<0&&(e.x=l.width),e.x>l.width&&(e.x=0),e.y<0&&(e.y=l.height),e.y>l.height&&(e.y=0),n.beginPath(),n.arc(e.x,e.y,e.size,0,2*Math.PI),n.fillStyle=e.color,n.globalAlpha=e.opacity,n.fill(),t.current.slice(a+1).forEach(t=>{let a=e.x-t.x,l=e.y-t.y,i=Math.sqrt(a*a+l*l);i<100&&(n.beginPath(),n.moveTo(e.x,e.y),n.lineTo(t.x,t.y),n.strokeStyle=e.color,n.globalAlpha=(1-i/100)*.2,n.lineWidth=.5,n.stroke())})}),n.globalAlpha=1},r=()=>{o(),a.current=requestAnimationFrame(r)};i(),s(),r();let c=()=>{i(),s()};return window.addEventListener("resize",c),()=>{window.removeEventListener("resize",c),a.current&&cancelAnimationFrame(a.current)}},[]),(0,l.jsx)("canvas",{ref:e,className:"absolute inset-0 pointer-events-none opacity-30",style:{zIndex:1}})}function s(){let[e,t]=(0,n.useState)(!1);return(0,n.useEffect)(()=>{t(!0)},[]),(0,l.jsxs)("div",{className:"min-h-screen gradient-bg relative overflow-hidden",children:[(0,l.jsx)(i,{}),(0,l.jsxs)("div",{className:"absolute inset-0 opacity-10",children:[(0,l.jsx)("div",{className:"absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500 rounded-full blur-3xl animate-pulse-slow"}),(0,l.jsx)("div",{className:"absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500 rounded-full blur-3xl animate-pulse-slow",style:{animationDelay:"1.5s"}})]}),(0,l.jsxs)("main",{className:"relative z-10 flex flex-col items-center justify-center min-h-screen px-6 sm:px-8 lg:px-12",children:[(0,l.jsx)("div",{className:"text-center w-full max-w-7xl mx-auto",children:(0,l.jsx)("h1",{className:"font-inter-tight font-black text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl leading-none tracking-tighter gradient-text transition-all duration-1000 ".concat(e?"animate-fade-in-up opacity-100":"opacity-0"),children:"BlocksConnect"})}),(0,l.jsx)("div",{className:"absolute top-20 left-10 w-4 h-4 bg-blue-400 rounded-full animate-float opacity-60"}),(0,l.jsx)("div",{className:"absolute top-40 right-20 w-6 h-6 bg-purple-400 rounded-full animate-float opacity-40",style:{animationDelay:"2s"}}),(0,l.jsx)("div",{className:"absolute bottom-40 left-20 w-3 h-3 bg-green-400 rounded-full animate-float opacity-50",style:{animationDelay:"4s"}}),(0,l.jsx)("div",{className:"absolute bottom-20 right-10 w-5 h-5 bg-yellow-400 rounded-full animate-float opacity-30",style:{animationDelay:"3s"}})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[441,517,358],()=>t(5497)),_N_E=e.O()}]);