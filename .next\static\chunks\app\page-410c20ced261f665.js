(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{5497:(e,t,a)=>{Promise.resolve().then(a.bind(a,1347))},1347:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>o});var l=a(5155),i=a(2115);function n(){let e=(0,i.useRef)(null),t=(0,i.useRef)([]),a=(0,i.useRef)(null);return(0,i.useEffect)(()=>{let l=e.current;if(!l)return;let i=l.getContext("2d");if(!i)return;let n=()=>{l.width=window.innerWidth,l.height=window.innerHeight},o=()=>{let e=[],a=Math.min(50,Math.floor(l.width*l.height/15e3));for(let t=0;t<a;t++)e.push({x:Math.random()*l.width,y:Math.random()*l.height,vx:(Math.random()-.5)*.5,vy:(Math.random()-.5)*.5,size:2*Math.random()+1,opacity:.5*Math.random()+.1,color:["#3b82f6","#8b5cf6","#06b6d4","#10b981"][Math.floor(4*Math.random())]});t.current=e},r=()=>{i.clearRect(0,0,l.width,l.height),t.current.forEach((e,a)=>{e.x+=e.vx,e.y+=e.vy,e.x<0&&(e.x=l.width),e.x>l.width&&(e.x=0),e.y<0&&(e.y=l.height),e.y>l.height&&(e.y=0),i.beginPath(),i.arc(e.x,e.y,e.size,0,2*Math.PI),i.fillStyle=e.color,i.globalAlpha=e.opacity,i.fill(),t.current.slice(a+1).forEach(t=>{let a=e.x-t.x,l=e.y-t.y,n=Math.sqrt(a*a+l*l);n<100&&(i.beginPath(),i.moveTo(e.x,e.y),i.lineTo(t.x,t.y),i.strokeStyle=e.color,i.globalAlpha=(1-n/100)*.2,i.lineWidth=.5,i.stroke())})}),i.globalAlpha=1},s=()=>{r(),a.current=requestAnimationFrame(s)};n(),o(),s();let c=()=>{n(),o()};return window.addEventListener("resize",c),()=>{window.removeEventListener("resize",c),a.current&&cancelAnimationFrame(a.current)}},[]),(0,l.jsx)("canvas",{ref:e,className:"absolute inset-0 pointer-events-none opacity-30",style:{zIndex:1}})}function o(){let[e,t]=(0,i.useState)(!1);return(0,i.useEffect)(()=>{t(!0)},[]),(0,l.jsxs)("div",{className:"min-h-screen gradient-bg relative overflow-hidden",children:[(0,l.jsx)(n,{}),(0,l.jsxs)("div",{className:"absolute inset-0 opacity-10",children:[(0,l.jsx)("div",{className:"absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500 rounded-full blur-3xl animate-pulse-slow"}),(0,l.jsx)("div",{className:"absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500 rounded-full blur-3xl animate-pulse-slow",style:{animationDelay:"1.5s"}})]}),(0,l.jsxs)("main",{className:"relative z-10 flex flex-col items-center justify-center min-h-screen px-4 sm:px-6 lg:px-8",children:[(0,l.jsxs)("div",{className:"text-center max-w-4xl mx-auto",children:[(0,l.jsx)("h1",{className:"font-inter-tight font-black text-6xl sm:text-7xl md:text-8xl lg:text-9xl xl:text-[10rem] leading-none tracking-tighter gradient-text mb-8 transition-all duration-1000 ".concat(e?"animate-fade-in-up opacity-100":"opacity-0"),children:"BlocksConnect"}),(0,l.jsx)("p",{className:"font-inter-tight font-light text-lg sm:text-xl md:text-2xl text-gray-300 mb-12 max-w-2xl mx-auto transition-all duration-1000 delay-300 ".concat(e?"animate-fade-in-up opacity-100":"opacity-0"),children:"Connecting the future, one block at a time"}),(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center transition-all duration-1000 delay-500 ".concat(e?"animate-fade-in-up opacity-100":"opacity-0"),children:[(0,l.jsxs)("button",{className:"group relative px-8 py-4 bg-white text-black font-inter-tight font-semibold text-lg rounded-lg hover-glow transition-all duration-300 hover:scale-105",children:[(0,l.jsx)("span",{className:"relative z-10",children:"Get Started"}),(0,l.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg opacity-0 group-hover:opacity-20 transition-opacity duration-300"})]}),(0,l.jsx)("button",{className:"group px-8 py-4 border border-gray-600 text-white font-inter-tight font-semibold text-lg rounded-lg hover:border-gray-400 transition-all duration-300 hover:scale-105 hover:bg-white/5",children:"Learn More"})]})]}),(0,l.jsx)("div",{className:"absolute top-20 left-10 w-4 h-4 bg-blue-400 rounded-full animate-float opacity-60"}),(0,l.jsx)("div",{className:"absolute top-40 right-20 w-6 h-6 bg-purple-400 rounded-full animate-float opacity-40",style:{animationDelay:"2s"}}),(0,l.jsx)("div",{className:"absolute bottom-40 left-20 w-3 h-3 bg-green-400 rounded-full animate-float opacity-50",style:{animationDelay:"4s"}}),(0,l.jsx)("div",{className:"absolute bottom-20 right-10 w-5 h-5 bg-yellow-400 rounded-full animate-float opacity-30",style:{animationDelay:"3s"}})]}),(0,l.jsx)("footer",{className:"absolute bottom-0 left-0 right-0 p-6 text-center transition-all duration-1000 delay-700 ".concat(e?"animate-fade-in opacity-100":"opacity-0"),children:(0,l.jsx)("p",{className:"font-inter-tight text-sm text-gray-500",children:"\xa9 2024 BlocksConnect. Building the decentralized future."})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[441,517,358],()=>t(5497)),_N_E=e.O()}]);