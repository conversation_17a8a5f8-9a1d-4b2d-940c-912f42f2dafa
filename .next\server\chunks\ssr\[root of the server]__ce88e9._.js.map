{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[next]/internal/font/google/inter_e5387405.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_export_value__({\n  \"className\": \"inter_e5387405-module__6kjfMG__className\",\n  \"variable\": \"inter_e5387405-module__6kjfMG__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[next]/internal/font/google/inter_e5387405.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-inter%22,%22display%22:%22swap%22}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,wJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,wJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,wJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0]}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Reactprojects/landingpage-blocksconnect/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata, Viewport } from \"next\";\nimport { Inter } from \"next/font/google\";\nimport \"./globals.css\";\n\nconst inter = Inter({\n  subsets: [\"latin\"],\n  variable: \"--font-inter\",\n  display: 'swap',\n});\n\nexport const metadata: Metadata = {\n  title: \"BlocksConnect - Connecting the Future\",\n  description: \"BlocksConnect is building the decentralized future, one block at a time. Experience the next generation of blockchain connectivity.\",\n  keywords: [\"blockchain\", \"decentralized\", \"web3\", \"crypto\", \"blocks\", \"connect\"],\n  authors: [{ name: \"BlocksConnect Team\" }],\n  creator: \"BlocksConnect\",\n  publisher: \"BlocksConnect\",\n  robots: {\n    index: true,\n    follow: true,\n  },\n  openGraph: {\n    type: \"website\",\n    locale: \"en_US\",\n    url: \"https://blocksconnect.com\",\n    title: \"BlocksConnect - Connecting the Future\",\n    description: \"BlocksConnect is building the decentralized future, one block at a time.\",\n    siteName: \"BlocksConnect\",\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: \"BlocksConnect - Connecting the Future\",\n    description: \"BlocksConnect is building the decentralized future, one block at a time.\",\n    creator: \"@blocksconnect\",\n  },\n};\n\nexport const viewport: Viewport = {\n  width: \"device-width\",\n  initialScale: 1,\n  maximumScale: 1,\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\" className=\"dark\">\n      <body\n        className={`${inter.variable} font-inter-tight antialiased bg-black text-white`}\n      >\n        {children}\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAUO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QAAC;QAAc;QAAiB;QAAQ;QAAU;QAAU;KAAU;IAChF,SAAS;QAAC;YAAE,MAAM;QAAqB;KAAE;IACzC,SAAS;IACT,WAAW;IACX,QAAQ;QACN,OAAO;QACP,QAAQ;IACV;IACA,WAAW;QACT,MAAM;QACN,QAAQ;QACR,KAAK;QACL,OAAO;QACP,aAAa;QACb,UAAU;IACZ;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,SAAS;IACX;AACF;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,cAAc;IACd,cAAc;AAChB;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAK,WAAU;kBACxB,cAAA,8OAAC;YACC,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,iDAAiD,CAAC;sBAE9E;;;;;;;;;;;AAIT"}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Reactprojects/landingpage-blocksconnect/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,0HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0]}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}