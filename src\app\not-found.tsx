'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import AnimatedBackground from '@/components/AnimatedBackground';

export default function NotFound() {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  return (
    <div className="min-h-screen gradient-bg relative overflow-hidden">
      {/* Animated Background */}
      <AnimatedBackground />
      
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-red-500 rounded-full blur-3xl animate-pulse-slow"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-orange-500 rounded-full blur-3xl animate-pulse-slow" style={{ animationDelay: '1.5s' }}></div>
      </div>

      {/* Main Content */}
      <main className="relative z-10 flex flex-col items-center justify-center min-h-screen px-6 sm:px-8 lg:px-12">
        {/* Error Section */}
        <div className="text-center w-full max-w-7xl mx-auto">
          {/* 404 Number */}
          <div 
            className={`font-inter-tight font-black text-6xl sm:text-7xl md:text-8xl lg:text-9xl xl:text-[12rem] leading-none tracking-tighter text-white/20 mb-4 transition-all duration-1000 ${
              isLoaded ? 'animate-fade-in-up opacity-100' : 'opacity-0'
            }`}
          >
            404
          </div>

          {/* Main Heading */}
          <h1 
            className={`font-inter-tight font-black text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl leading-none tracking-tighter gradient-text mb-6 transition-all duration-1000 delay-300 ${
              isLoaded ? 'animate-fade-in-up opacity-100' : 'opacity-0'
            }`}
          >
            Block Not Found
          </h1>

          {/* Subtitle */}
          <p 
            className={`font-inter-tight font-light text-lg sm:text-xl md:text-2xl text-gray-300 mb-8 max-w-2xl mx-auto transition-all duration-1000 delay-500 ${
              isLoaded ? 'animate-fade-in-up opacity-100' : 'opacity-0'
            }`}
          >
            This connection seems to be broken. Let's get you back to the main chain.
          </p>

          {/* Back to Home Button */}
          <div 
            className={`transition-all duration-1000 delay-700 ${
              isLoaded ? 'animate-fade-in-up opacity-100' : 'opacity-0'
            }`}
          >
            <Link 
              href="/"
              className="group relative inline-flex px-8 py-4 bg-white text-black font-inter-tight font-semibold text-lg rounded-lg hover-glow transition-all duration-300 hover:scale-105"
            >
              <span className="relative z-10">Return to BlocksConnect</span>
              <div className="absolute inset-0 bg-gradient-to-r from-red-500 to-orange-600 rounded-lg opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
            </Link>
          </div>
        </div>

        {/* Floating Elements - Different colors for error state */}
        <div className="absolute top-20 left-10 w-4 h-4 bg-red-400 rounded-full animate-float opacity-60"></div>
        <div className="absolute top-40 right-20 w-6 h-6 bg-orange-400 rounded-full animate-float opacity-40" style={{ animationDelay: '2s' }}></div>
        <div className="absolute bottom-40 left-20 w-3 h-3 bg-yellow-400 rounded-full animate-float opacity-50" style={{ animationDelay: '4s' }}></div>
        <div className="absolute bottom-20 right-10 w-5 h-5 bg-pink-400 rounded-full animate-float opacity-30" style={{ animationDelay: '3s' }}></div>
      </main>
    </div>
  );
}
