version: '3.8'

services:
  blocksconnect-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: blocksconnect-landing-dev
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - NEXT_TELEMETRY_DISABLED=1
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    restart: unless-stopped
    networks:
      - blocksconnect-dev-network
    command: npm run dev

networks:
  blocksconnect-dev-network:
    driver: bridge
