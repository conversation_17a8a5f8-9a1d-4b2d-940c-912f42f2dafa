{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Reactprojects/landingpage-blocksconnect/src/components/AnimatedBackground.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\n\ninterface Particle {\n  x: number;\n  y: number;\n  vx: number;\n  vy: number;\n  size: number;\n  opacity: number;\n  color: string;\n}\n\nexport default function AnimatedBackground() {\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const particlesRef = useRef<Particle[]>([]);\n  const animationRef = useRef<number | null>(null);\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    const resizeCanvas = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n    };\n\n    const createParticles = () => {\n      const particles: Particle[] = [];\n      const particleCount = Math.min(50, Math.floor((canvas.width * canvas.height) / 15000));\n\n      for (let i = 0; i < particleCount; i++) {\n        particles.push({\n          x: Math.random() * canvas.width,\n          y: Math.random() * canvas.height,\n          vx: (Math.random() - 0.5) * 0.5,\n          vy: (Math.random() - 0.5) * 0.5,\n          size: Math.random() * 2 + 1,\n          opacity: Math.random() * 0.5 + 0.1,\n          color: ['#3b82f6', '#8b5cf6', '#06b6d4', '#10b981'][Math.floor(Math.random() * 4)]\n        });\n      }\n\n      particlesRef.current = particles;\n    };\n\n    const drawParticles = () => {\n      ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n      particlesRef.current.forEach((particle, index) => {\n        // Update position\n        particle.x += particle.vx;\n        particle.y += particle.vy;\n\n        // Wrap around edges\n        if (particle.x < 0) particle.x = canvas.width;\n        if (particle.x > canvas.width) particle.x = 0;\n        if (particle.y < 0) particle.y = canvas.height;\n        if (particle.y > canvas.height) particle.y = 0;\n\n        // Draw particle\n        ctx.beginPath();\n        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);\n        ctx.fillStyle = particle.color;\n        ctx.globalAlpha = particle.opacity;\n        ctx.fill();\n\n        // Draw connections\n        particlesRef.current.slice(index + 1).forEach(otherParticle => {\n          const dx = particle.x - otherParticle.x;\n          const dy = particle.y - otherParticle.y;\n          const distance = Math.sqrt(dx * dx + dy * dy);\n\n          if (distance < 100) {\n            ctx.beginPath();\n            ctx.moveTo(particle.x, particle.y);\n            ctx.lineTo(otherParticle.x, otherParticle.y);\n            ctx.strokeStyle = particle.color;\n            ctx.globalAlpha = (1 - distance / 100) * 0.2;\n            ctx.lineWidth = 0.5;\n            ctx.stroke();\n          }\n        });\n      });\n\n      ctx.globalAlpha = 1;\n    };\n\n    const animate = () => {\n      drawParticles();\n      animationRef.current = requestAnimationFrame(animate);\n    };\n\n    resizeCanvas();\n    createParticles();\n    animate();\n\n    const handleResize = () => {\n      resizeCanvas();\n      createParticles();\n    };\n\n    window.addEventListener('resize', handleResize);\n\n    return () => {\n      window.removeEventListener('resize', handleResize);\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n    };\n  }, []);\n\n  return (\n    <canvas\n      ref={canvasRef}\n      className=\"absolute inset-0 pointer-events-none opacity-30\"\n      style={{ zIndex: 1 }}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAce,SAAS;;IACtB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAc,EAAE;IAC1C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAiB;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM,SAAS,UAAU,OAAO;YAChC,IAAI,CAAC,QAAQ;YAEb,MAAM,MAAM,OAAO,UAAU,CAAC;YAC9B,IAAI,CAAC,KAAK;YAEV,MAAM;6DAAe;oBACnB,OAAO,KAAK,GAAG,OAAO,UAAU;oBAChC,OAAO,MAAM,GAAG,OAAO,WAAW;gBACpC;;YAEA,MAAM;gEAAkB;oBACtB,MAAM,YAAwB,EAAE;oBAChC,MAAM,gBAAgB,KAAK,GAAG,CAAC,IAAI,KAAK,KAAK,CAAC,AAAC,OAAO,KAAK,GAAG,OAAO,MAAM,GAAI;oBAE/E,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;wBACtC,UAAU,IAAI,CAAC;4BACb,GAAG,KAAK,MAAM,KAAK,OAAO,KAAK;4BAC/B,GAAG,KAAK,MAAM,KAAK,OAAO,MAAM;4BAChC,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;4BAC5B,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;4BAC5B,MAAM,KAAK,MAAM,KAAK,IAAI;4BAC1B,SAAS,KAAK,MAAM,KAAK,MAAM;4BAC/B,OAAO;gCAAC;gCAAW;gCAAW;gCAAW;6BAAU,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,GAAG;wBACpF;oBACF;oBAEA,aAAa,OAAO,GAAG;gBACzB;;YAEA,MAAM;8DAAgB;oBACpB,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;oBAE/C,aAAa,OAAO,CAAC,OAAO;sEAAC,CAAC,UAAU;4BACtC,kBAAkB;4BAClB,SAAS,CAAC,IAAI,SAAS,EAAE;4BACzB,SAAS,CAAC,IAAI,SAAS,EAAE;4BAEzB,oBAAoB;4BACpB,IAAI,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG,OAAO,KAAK;4BAC7C,IAAI,SAAS,CAAC,GAAG,OAAO,KAAK,EAAE,SAAS,CAAC,GAAG;4BAC5C,IAAI,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG,OAAO,MAAM;4BAC9C,IAAI,SAAS,CAAC,GAAG,OAAO,MAAM,EAAE,SAAS,CAAC,GAAG;4BAE7C,gBAAgB;4BAChB,IAAI,SAAS;4BACb,IAAI,GAAG,CAAC,SAAS,CAAC,EAAE,SAAS,CAAC,EAAE,SAAS,IAAI,EAAE,GAAG,KAAK,EAAE,GAAG;4BAC5D,IAAI,SAAS,GAAG,SAAS,KAAK;4BAC9B,IAAI,WAAW,GAAG,SAAS,OAAO;4BAClC,IAAI,IAAI;4BAER,mBAAmB;4BACnB,aAAa,OAAO,CAAC,KAAK,CAAC,QAAQ,GAAG,OAAO;8EAAC,CAAA;oCAC5C,MAAM,KAAK,SAAS,CAAC,GAAG,cAAc,CAAC;oCACvC,MAAM,KAAK,SAAS,CAAC,GAAG,cAAc,CAAC;oCACvC,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK;oCAE1C,IAAI,WAAW,KAAK;wCAClB,IAAI,SAAS;wCACb,IAAI,MAAM,CAAC,SAAS,CAAC,EAAE,SAAS,CAAC;wCACjC,IAAI,MAAM,CAAC,cAAc,CAAC,EAAE,cAAc,CAAC;wCAC3C,IAAI,WAAW,GAAG,SAAS,KAAK;wCAChC,IAAI,WAAW,GAAG,CAAC,IAAI,WAAW,GAAG,IAAI;wCACzC,IAAI,SAAS,GAAG;wCAChB,IAAI,MAAM;oCACZ;gCACF;;wBACF;;oBAEA,IAAI,WAAW,GAAG;gBACpB;;YAEA,MAAM;wDAAU;oBACd;oBACA,aAAa,OAAO,GAAG,sBAAsB;gBAC/C;;YAEA;YACA;YACA;YAEA,MAAM;6DAAe;oBACnB;oBACA;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAElC;gDAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;oBACrC,IAAI,aAAa,OAAO,EAAE;wBACxB,qBAAqB,aAAa,OAAO;oBAC3C;gBACF;;QACF;uCAAG,EAAE;IAEL,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;QACV,OAAO;YAAE,QAAQ;QAAE;;;;;;AAGzB;GA7GwB;KAAA"}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Reactprojects/landingpage-blocksconnect/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport AnimatedBackground from '@/components/AnimatedBackground';\n\nexport default function Home() {\n  const [isLoaded, setIsLoaded] = useState(false);\n\n  useEffect(() => {\n    setIsLoaded(true);\n  }, []);\n\n  return (\n    <div className=\"min-h-screen gradient-bg relative overflow-hidden\">\n      {/* Animated Background */}\n      <AnimatedBackground />\n\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500 rounded-full blur-3xl animate-pulse-slow\"></div>\n        <div className=\"absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500 rounded-full blur-3xl animate-pulse-slow\" style={{ animationDelay: '1.5s' }}></div>\n      </div>\n\n      {/* Main Content */}\n      <main className=\"relative z-10 flex flex-col items-center justify-center min-h-screen px-6 sm:px-8 lg:px-12\">\n        {/* Hero Section */}\n        <div className=\"text-center w-full max-w-7xl mx-auto\">\n          {/* Main Heading */}\n          <h1\n            className={`font-inter-tight font-black text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl leading-none tracking-tighter gradient-text transition-all duration-1000 ${\n              isLoaded ? 'animate-fade-in-up opacity-100' : 'opacity-0'\n            }`}\n          >\n            BlocksConnect\n          </h1>\n        </div>\n\n        {/* Floating Elements */}\n        <div className=\"absolute top-20 left-10 w-4 h-4 bg-blue-400 rounded-full animate-float opacity-60\"></div>\n        <div className=\"absolute top-40 right-20 w-6 h-6 bg-purple-400 rounded-full animate-float opacity-40\" style={{ animationDelay: '2s' }}></div>\n        <div className=\"absolute bottom-40 left-20 w-3 h-3 bg-green-400 rounded-full animate-float opacity-50\" style={{ animationDelay: '4s' }}></div>\n        <div className=\"absolute bottom-20 right-10 w-5 h-5 bg-yellow-400 rounded-full animate-float opacity-30\" style={{ animationDelay: '3s' }}></div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,YAAY;QACd;yBAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,2IAAA,CAAA,UAAkB;;;;;0BAGnB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;wBAAiG,OAAO;4BAAE,gBAAgB;wBAAO;;;;;;;;;;;;0BAIlJ,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAI,WAAU;kCAEb,cAAA,6LAAC;4BACC,WAAW,CAAC,8JAA8J,EACxK,WAAW,mCAAmC,aAC9C;sCACH;;;;;;;;;;;kCAMH,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;wBAAuF,OAAO;4BAAE,gBAAgB;wBAAK;;;;;;kCACpI,6LAAC;wBAAI,WAAU;wBAAwF,OAAO;4BAAE,gBAAgB;wBAAK;;;;;;kCACrI,6LAAC;wBAAI,WAAU;wBAA0F,OAAO;4BAAE,gBAAgB;wBAAK;;;;;;;;;;;;;;;;;;AAI/I;GAxCwB;KAAA"}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 286, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Reactprojects/landingpage-blocksconnect/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE$2\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PORTAL_TYPE:\n          return \"Portal\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function disabledLog() {}\n    function disableLogs() {\n      if (0 === disabledDepth) {\n        prevLog = console.log;\n        prevInfo = console.info;\n        prevWarn = console.warn;\n        prevError = console.error;\n        prevGroup = console.group;\n        prevGroupCollapsed = console.groupCollapsed;\n        prevGroupEnd = console.groupEnd;\n        var props = {\n          configurable: !0,\n          enumerable: !0,\n          value: disabledLog,\n          writable: !0\n        };\n        Object.defineProperties(console, {\n          info: props,\n          log: props,\n          warn: props,\n          error: props,\n          group: props,\n          groupCollapsed: props,\n          groupEnd: props\n        });\n      }\n      disabledDepth++;\n    }\n    function reenableLogs() {\n      disabledDepth--;\n      if (0 === disabledDepth) {\n        var props = { configurable: !0, enumerable: !0, writable: !0 };\n        Object.defineProperties(console, {\n          log: assign({}, props, { value: prevLog }),\n          info: assign({}, props, { value: prevInfo }),\n          warn: assign({}, props, { value: prevWarn }),\n          error: assign({}, props, { value: prevError }),\n          group: assign({}, props, { value: prevGroup }),\n          groupCollapsed: assign({}, props, { value: prevGroupCollapsed }),\n          groupEnd: assign({}, props, { value: prevGroupEnd })\n        });\n      }\n      0 > disabledDepth &&\n        console.error(\n          \"disabledDepth fell below zero. This is a bug in React. Please file an issue.\"\n        );\n    }\n    function describeBuiltInComponentFrame(name) {\n      if (void 0 === prefix)\n        try {\n          throw Error();\n        } catch (x) {\n          var match = x.stack.trim().match(/\\n( *(at )?)/);\n          prefix = (match && match[1]) || \"\";\n          suffix =\n            -1 < x.stack.indexOf(\"\\n    at\")\n              ? \" (<anonymous>)\"\n              : -1 < x.stack.indexOf(\"@\")\n                ? \"@unknown:0:0\"\n                : \"\";\n        }\n      return \"\\n\" + prefix + name + suffix;\n    }\n    function describeNativeComponentFrame(fn, construct) {\n      if (!fn || reentry) return \"\";\n      var frame = componentFrameCache.get(fn);\n      if (void 0 !== frame) return frame;\n      reentry = !0;\n      frame = Error.prepareStackTrace;\n      Error.prepareStackTrace = void 0;\n      var previousDispatcher = null;\n      previousDispatcher = ReactSharedInternals.H;\n      ReactSharedInternals.H = null;\n      disableLogs();\n      try {\n        var RunInRootFrame = {\n          DetermineComponentFrameRoot: function () {\n            try {\n              if (construct) {\n                var Fake = function () {\n                  throw Error();\n                };\n                Object.defineProperty(Fake.prototype, \"props\", {\n                  set: function () {\n                    throw Error();\n                  }\n                });\n                if (\"object\" === typeof Reflect && Reflect.construct) {\n                  try {\n                    Reflect.construct(Fake, []);\n                  } catch (x) {\n                    var control = x;\n                  }\n                  Reflect.construct(fn, [], Fake);\n                } else {\n                  try {\n                    Fake.call();\n                  } catch (x$0) {\n                    control = x$0;\n                  }\n                  fn.call(Fake.prototype);\n                }\n              } else {\n                try {\n                  throw Error();\n                } catch (x$1) {\n                  control = x$1;\n                }\n                (Fake = fn()) &&\n                  \"function\" === typeof Fake.catch &&\n                  Fake.catch(function () {});\n              }\n            } catch (sample) {\n              if (sample && control && \"string\" === typeof sample.stack)\n                return [sample.stack, control.stack];\n            }\n            return [null, null];\n          }\n        };\n        RunInRootFrame.DetermineComponentFrameRoot.displayName =\n          \"DetermineComponentFrameRoot\";\n        var namePropDescriptor = Object.getOwnPropertyDescriptor(\n          RunInRootFrame.DetermineComponentFrameRoot,\n          \"name\"\n        );\n        namePropDescriptor &&\n          namePropDescriptor.configurable &&\n          Object.defineProperty(\n            RunInRootFrame.DetermineComponentFrameRoot,\n            \"name\",\n            { value: \"DetermineComponentFrameRoot\" }\n          );\n        var _RunInRootFrame$Deter =\n            RunInRootFrame.DetermineComponentFrameRoot(),\n          sampleStack = _RunInRootFrame$Deter[0],\n          controlStack = _RunInRootFrame$Deter[1];\n        if (sampleStack && controlStack) {\n          var sampleLines = sampleStack.split(\"\\n\"),\n            controlLines = controlStack.split(\"\\n\");\n          for (\n            _RunInRootFrame$Deter = namePropDescriptor = 0;\n            namePropDescriptor < sampleLines.length &&\n            !sampleLines[namePropDescriptor].includes(\n              \"DetermineComponentFrameRoot\"\n            );\n\n          )\n            namePropDescriptor++;\n          for (\n            ;\n            _RunInRootFrame$Deter < controlLines.length &&\n            !controlLines[_RunInRootFrame$Deter].includes(\n              \"DetermineComponentFrameRoot\"\n            );\n\n          )\n            _RunInRootFrame$Deter++;\n          if (\n            namePropDescriptor === sampleLines.length ||\n            _RunInRootFrame$Deter === controlLines.length\n          )\n            for (\n              namePropDescriptor = sampleLines.length - 1,\n                _RunInRootFrame$Deter = controlLines.length - 1;\n              1 <= namePropDescriptor &&\n              0 <= _RunInRootFrame$Deter &&\n              sampleLines[namePropDescriptor] !==\n                controlLines[_RunInRootFrame$Deter];\n\n            )\n              _RunInRootFrame$Deter--;\n          for (\n            ;\n            1 <= namePropDescriptor && 0 <= _RunInRootFrame$Deter;\n            namePropDescriptor--, _RunInRootFrame$Deter--\n          )\n            if (\n              sampleLines[namePropDescriptor] !==\n              controlLines[_RunInRootFrame$Deter]\n            ) {\n              if (1 !== namePropDescriptor || 1 !== _RunInRootFrame$Deter) {\n                do\n                  if (\n                    (namePropDescriptor--,\n                    _RunInRootFrame$Deter--,\n                    0 > _RunInRootFrame$Deter ||\n                      sampleLines[namePropDescriptor] !==\n                        controlLines[_RunInRootFrame$Deter])\n                  ) {\n                    var _frame =\n                      \"\\n\" +\n                      sampleLines[namePropDescriptor].replace(\n                        \" at new \",\n                        \" at \"\n                      );\n                    fn.displayName &&\n                      _frame.includes(\"<anonymous>\") &&\n                      (_frame = _frame.replace(\"<anonymous>\", fn.displayName));\n                    \"function\" === typeof fn &&\n                      componentFrameCache.set(fn, _frame);\n                    return _frame;\n                  }\n                while (1 <= namePropDescriptor && 0 <= _RunInRootFrame$Deter);\n              }\n              break;\n            }\n        }\n      } finally {\n        (reentry = !1),\n          (ReactSharedInternals.H = previousDispatcher),\n          reenableLogs(),\n          (Error.prepareStackTrace = frame);\n      }\n      sampleLines = (sampleLines = fn ? fn.displayName || fn.name : \"\")\n        ? describeBuiltInComponentFrame(sampleLines)\n        : \"\";\n      \"function\" === typeof fn && componentFrameCache.set(fn, sampleLines);\n      return sampleLines;\n    }\n    function describeUnknownElementTypeFrameInDEV(type) {\n      if (null == type) return \"\";\n      if (\"function\" === typeof type) {\n        var prototype = type.prototype;\n        return describeNativeComponentFrame(\n          type,\n          !(!prototype || !prototype.isReactComponent)\n        );\n      }\n      if (\"string\" === typeof type) return describeBuiltInComponentFrame(type);\n      switch (type) {\n        case REACT_SUSPENSE_TYPE:\n          return describeBuiltInComponentFrame(\"Suspense\");\n        case REACT_SUSPENSE_LIST_TYPE:\n          return describeBuiltInComponentFrame(\"SuspenseList\");\n      }\n      if (\"object\" === typeof type)\n        switch (type.$$typeof) {\n          case REACT_FORWARD_REF_TYPE:\n            return (type = describeNativeComponentFrame(type.render, !1)), type;\n          case REACT_MEMO_TYPE:\n            return describeUnknownElementTypeFrameInDEV(type.type);\n          case REACT_LAZY_TYPE:\n            prototype = type._payload;\n            type = type._init;\n            try {\n              return describeUnknownElementTypeFrameInDEV(type(prototype));\n            } catch (x) {}\n        }\n      return \"\";\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(type, key, self, source, owner, props) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      if (\n        \"string\" === typeof type ||\n        \"function\" === typeof type ||\n        type === REACT_FRAGMENT_TYPE ||\n        type === REACT_PROFILER_TYPE ||\n        type === REACT_STRICT_MODE_TYPE ||\n        type === REACT_SUSPENSE_TYPE ||\n        type === REACT_SUSPENSE_LIST_TYPE ||\n        type === REACT_OFFSCREEN_TYPE ||\n        (\"object\" === typeof type &&\n          null !== type &&\n          (type.$$typeof === REACT_LAZY_TYPE ||\n            type.$$typeof === REACT_MEMO_TYPE ||\n            type.$$typeof === REACT_CONTEXT_TYPE ||\n            type.$$typeof === REACT_CONSUMER_TYPE ||\n            type.$$typeof === REACT_FORWARD_REF_TYPE ||\n            type.$$typeof === REACT_CLIENT_REFERENCE$1 ||\n            void 0 !== type.getModuleId))\n      ) {\n        var children = config.children;\n        if (void 0 !== children)\n          if (isStaticChildren)\n            if (isArrayImpl(children)) {\n              for (\n                isStaticChildren = 0;\n                isStaticChildren < children.length;\n                isStaticChildren++\n              )\n                validateChildKeys(children[isStaticChildren], type);\n              Object.freeze && Object.freeze(children);\n            } else\n              console.error(\n                \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n              );\n          else validateChildKeys(children, type);\n      } else {\n        children = \"\";\n        if (\n          void 0 === type ||\n          (\"object\" === typeof type &&\n            null !== type &&\n            0 === Object.keys(type).length)\n        )\n          children +=\n            \" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\";\n        null === type\n          ? (isStaticChildren = \"null\")\n          : isArrayImpl(type)\n            ? (isStaticChildren = \"array\")\n            : void 0 !== type && type.$$typeof === REACT_ELEMENT_TYPE\n              ? ((isStaticChildren =\n                  \"<\" +\n                  (getComponentNameFromType(type.type) || \"Unknown\") +\n                  \" />\"),\n                (children =\n                  \" Did you accidentally export a JSX literal instead of a component?\"))\n              : (isStaticChildren = typeof type);\n        console.error(\n          \"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",\n          isStaticChildren,\n          children\n        );\n      }\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(type, children, self, source, getOwner(), maybeKey);\n    }\n    function validateChildKeys(node, parentType) {\n      if (\n        \"object\" === typeof node &&\n        node &&\n        node.$$typeof !== REACT_CLIENT_REFERENCE\n      )\n        if (isArrayImpl(node))\n          for (var i = 0; i < node.length; i++) {\n            var child = node[i];\n            isValidElement(child) && validateExplicitKey(child, parentType);\n          }\n        else if (isValidElement(node))\n          node._store && (node._store.validated = 1);\n        else if (\n          (null === node || \"object\" !== typeof node\n            ? (i = null)\n            : ((i =\n                (MAYBE_ITERATOR_SYMBOL && node[MAYBE_ITERATOR_SYMBOL]) ||\n                node[\"@@iterator\"]),\n              (i = \"function\" === typeof i ? i : null)),\n          \"function\" === typeof i &&\n            i !== node.entries &&\n            ((i = i.call(node)), i !== node))\n        )\n          for (; !(node = i.next()).done; )\n            isValidElement(node.value) &&\n              validateExplicitKey(node.value, parentType);\n    }\n    function isValidElement(object) {\n      return (\n        \"object\" === typeof object &&\n        null !== object &&\n        object.$$typeof === REACT_ELEMENT_TYPE\n      );\n    }\n    function validateExplicitKey(element, parentType) {\n      if (\n        element._store &&\n        !element._store.validated &&\n        null == element.key &&\n        ((element._store.validated = 1),\n        (parentType = getCurrentComponentErrorInfo(parentType)),\n        !ownerHasKeyUseWarning[parentType])\n      ) {\n        ownerHasKeyUseWarning[parentType] = !0;\n        var childOwner = \"\";\n        element &&\n          null != element._owner &&\n          element._owner !== getOwner() &&\n          ((childOwner = null),\n          \"number\" === typeof element._owner.tag\n            ? (childOwner = getComponentNameFromType(element._owner.type))\n            : \"string\" === typeof element._owner.name &&\n              (childOwner = element._owner.name),\n          (childOwner = \" It was passed a child from \" + childOwner + \".\"));\n        var prevGetCurrentStack = ReactSharedInternals.getCurrentStack;\n        ReactSharedInternals.getCurrentStack = function () {\n          var stack = describeUnknownElementTypeFrameInDEV(element.type);\n          prevGetCurrentStack && (stack += prevGetCurrentStack() || \"\");\n          return stack;\n        };\n        console.error(\n          'Each child in a list should have a unique \"key\" prop.%s%s See https://react.dev/link/warning-keys for more information.',\n          parentType,\n          childOwner\n        );\n        ReactSharedInternals.getCurrentStack = prevGetCurrentStack;\n      }\n    }\n    function getCurrentComponentErrorInfo(parentType) {\n      var info = \"\",\n        owner = getOwner();\n      owner &&\n        (owner = getComponentNameFromType(owner.type)) &&\n        (info = \"\\n\\nCheck the render method of `\" + owner + \"`.\");\n      info ||\n        ((parentType = getComponentNameFromType(parentType)) &&\n          (info =\n            \"\\n\\nCheck the top-level render call using <\" + parentType + \">.\"));\n      return info;\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_OFFSCREEN_TYPE = Symbol.for(\"react.offscreen\"),\n      MAYBE_ITERATOR_SYMBOL = Symbol.iterator,\n      REACT_CLIENT_REFERENCE$2 = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      assign = Object.assign,\n      REACT_CLIENT_REFERENCE$1 = Symbol.for(\"react.client.reference\"),\n      isArrayImpl = Array.isArray,\n      disabledDepth = 0,\n      prevLog,\n      prevInfo,\n      prevWarn,\n      prevError,\n      prevGroup,\n      prevGroupCollapsed,\n      prevGroupEnd;\n    disabledLog.__reactDisabledLog = !0;\n    var prefix,\n      suffix,\n      reentry = !1;\n    var componentFrameCache = new (\n      \"function\" === typeof WeakMap ? WeakMap : Map\n    )();\n    var REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var didWarnAboutKeySpread = {},\n      ownerHasKeyUseWarning = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      return jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self);\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,2BACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,eAAe;IACxB,SAAS;QACP,IAAI,MAAM,eAAe;YACvB,UAAU,QAAQ,GAAG;YACrB,WAAW,QAAQ,IAAI;YACvB,WAAW,QAAQ,IAAI;YACvB,YAAY,QAAQ,KAAK;YACzB,YAAY,QAAQ,KAAK;YACzB,qBAAqB,QAAQ,cAAc;YAC3C,eAAe,QAAQ,QAAQ;YAC/B,IAAI,QAAQ;gBACV,cAAc,CAAC;gBACf,YAAY,CAAC;gBACb,OAAO;gBACP,UAAU,CAAC;YACb;YACA,OAAO,gBAAgB,CAAC,SAAS;gBAC/B,MAAM;gBACN,KAAK;gBACL,MAAM;gBACN,OAAO;gBACP,OAAO;gBACP,gBAAgB;gBAChB,UAAU;YACZ;QACF;QACA;IACF;IACA,SAAS;QACP;QACA,IAAI,MAAM,eAAe;YACvB,IAAI,QAAQ;gBAAE,cAAc,CAAC;gBAAG,YAAY,CAAC;gBAAG,UAAU,CAAC;YAAE;YAC7D,OAAO,gBAAgB,CAAC,SAAS;gBAC/B,KAAK,OAAO,CAAC,GAAG,OAAO;oBAAE,OAAO;gBAAQ;gBACxC,MAAM,OAAO,CAAC,GAAG,OAAO;oBAAE,OAAO;gBAAS;gBAC1C,MAAM,OAAO,CAAC,GAAG,OAAO;oBAAE,OAAO;gBAAS;gBAC1C,OAAO,OAAO,CAAC,GAAG,OAAO;oBAAE,OAAO;gBAAU;gBAC5C,OAAO,OAAO,CAAC,GAAG,OAAO;oBAAE,OAAO;gBAAU;gBAC5C,gBAAgB,OAAO,CAAC,GAAG,OAAO;oBAAE,OAAO;gBAAmB;gBAC9D,UAAU,OAAO,CAAC,GAAG,OAAO;oBAAE,OAAO;gBAAa;YACpD;QACF;QACA,IAAI,iBACF,QAAQ,KAAK,CACX;IAEN;IACA,SAAS,8BAA8B,IAAI;QACzC,IAAI,KAAK,MAAM,QACb,IAAI;YACF,MAAM;QACR,EAAE,OAAO,GAAG;YACV,IAAI,QAAQ,EAAE,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC;YACjC,SAAS,AAAC,SAAS,KAAK,CAAC,EAAE,IAAK;YAChC,SACE,CAAC,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,cACjB,mBACA,CAAC,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,OACnB,iBACA;QACV;QACF,OAAO,OAAO,SAAS,OAAO;IAChC;IACA,SAAS,6BAA6B,EAAE,EAAE,SAAS;QACjD,IAAI,CAAC,MAAM,SAAS,OAAO;QAC3B,IAAI,QAAQ,oBAAoB,GAAG,CAAC;QACpC,IAAI,KAAK,MAAM,OAAO,OAAO;QAC7B,UAAU,CAAC;QACX,QAAQ,MAAM,iBAAiB;QAC/B,MAAM,iBAAiB,GAAG,KAAK;QAC/B,IAAI,qBAAqB;QACzB,qBAAqB,qBAAqB,CAAC;QAC3C,qBAAqB,CAAC,GAAG;QACzB;QACA,IAAI;YACF,IAAI,iBAAiB;gBACnB,6BAA6B;oBAC3B,IAAI;wBACF,IAAI,WAAW;4BACb,IAAI,OAAO;gCACT,MAAM;4BACR;4BACA,OAAO,cAAc,CAAC,KAAK,SAAS,EAAE,SAAS;gCAC7C,KAAK;oCACH,MAAM;gCACR;4BACF;4BACA,IAAI,aAAa,OAAO,WAAW,QAAQ,SAAS,EAAE;gCACpD,IAAI;oCACF,QAAQ,SAAS,CAAC,MAAM,EAAE;gCAC5B,EAAE,OAAO,GAAG;oCACV,IAAI,UAAU;gCAChB;gCACA,QAAQ,SAAS,CAAC,IAAI,EAAE,EAAE;4BAC5B,OAAO;gCACL,IAAI;oCACF,KAAK,IAAI;gCACX,EAAE,OAAO,KAAK;oCACZ,UAAU;gCACZ;gCACA,GAAG,IAAI,CAAC,KAAK,SAAS;4BACxB;wBACF,OAAO;4BACL,IAAI;gCACF,MAAM;4BACR,EAAE,OAAO,KAAK;gCACZ,UAAU;4BACZ;4BACA,CAAC,OAAO,IAAI,KACV,eAAe,OAAO,KAAK,KAAK,IAChC,KAAK,KAAK,CAAC,YAAa;wBAC5B;oBACF,EAAE,OAAO,QAAQ;wBACf,IAAI,UAAU,WAAW,aAAa,OAAO,OAAO,KAAK,EACvD,OAAO;4BAAC,OAAO,KAAK;4BAAE,QAAQ,KAAK;yBAAC;oBACxC;oBACA,OAAO;wBAAC;wBAAM;qBAAK;gBACrB;YACF;YACA,eAAe,2BAA2B,CAAC,WAAW,GACpD;YACF,IAAI,qBAAqB,OAAO,wBAAwB,CACtD,eAAe,2BAA2B,EAC1C;YAEF,sBACE,mBAAmB,YAAY,IAC/B,OAAO,cAAc,CACnB,eAAe,2BAA2B,EAC1C,QACA;gBAAE,OAAO;YAA8B;YAE3C,IAAI,wBACA,eAAe,2BAA2B,IAC5C,cAAc,qBAAqB,CAAC,EAAE,EACtC,eAAe,qBAAqB,CAAC,EAAE;YACzC,IAAI,eAAe,cAAc;gBAC/B,IAAI,cAAc,YAAY,KAAK,CAAC,OAClC,eAAe,aAAa,KAAK,CAAC;gBACpC,IACE,wBAAwB,qBAAqB,GAC7C,qBAAqB,YAAY,MAAM,IACvC,CAAC,WAAW,CAAC,mBAAmB,CAAC,QAAQ,CACvC,gCAIF;gBACF,MAEE,wBAAwB,aAAa,MAAM,IAC3C,CAAC,YAAY,CAAC,sBAAsB,CAAC,QAAQ,CAC3C,gCAIF;gBACF,IACE,uBAAuB,YAAY,MAAM,IACzC,0BAA0B,aAAa,MAAM,EAE7C,IACE,qBAAqB,YAAY,MAAM,GAAG,GACxC,wBAAwB,aAAa,MAAM,GAAG,GAChD,KAAK,sBACL,KAAK,yBACL,WAAW,CAAC,mBAAmB,KAC7B,YAAY,CAAC,sBAAsB,EAGrC;gBACJ,MAEE,KAAK,sBAAsB,KAAK,uBAChC,sBAAsB,wBAEtB,IACE,WAAW,CAAC,mBAAmB,KAC/B,YAAY,CAAC,sBAAsB,EACnC;oBACA,IAAI,MAAM,sBAAsB,MAAM,uBAAuB;wBAC3D,GACE,IACG,sBACD,yBACA,IAAI,yBACF,WAAW,CAAC,mBAAmB,KAC7B,YAAY,CAAC,sBAAsB,EACvC;4BACA,IAAI,SACF,OACA,WAAW,CAAC,mBAAmB,CAAC,OAAO,CACrC,YACA;4BAEJ,GAAG,WAAW,IACZ,OAAO,QAAQ,CAAC,kBAChB,CAAC,SAAS,OAAO,OAAO,CAAC,eAAe,GAAG,WAAW,CAAC;4BACzD,eAAe,OAAO,MACpB,oBAAoB,GAAG,CAAC,IAAI;4BAC9B,OAAO;wBACT;+BACK,KAAK,sBAAsB,KAAK,sBAAuB;oBAChE;oBACA;gBACF;YACJ;QACF,SAAU;YACP,UAAU,CAAC,GACT,qBAAqB,CAAC,GAAG,oBAC1B,gBACC,MAAM,iBAAiB,GAAG;QAC/B;QACA,cAAc,CAAC,cAAc,KAAK,GAAG,WAAW,IAAI,GAAG,IAAI,GAAG,EAAE,IAC5D,8BAA8B,eAC9B;QACJ,eAAe,OAAO,MAAM,oBAAoB,GAAG,CAAC,IAAI;QACxD,OAAO;IACT;IACA,SAAS,qCAAqC,IAAI;QAChD,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MAAM;YAC9B,IAAI,YAAY,KAAK,SAAS;YAC9B,OAAO,6BACL,MACA,CAAC,CAAC,CAAC,aAAa,CAAC,UAAU,gBAAgB;QAE/C;QACA,IAAI,aAAa,OAAO,MAAM,OAAO,8BAA8B;QACnE,OAAQ;YACN,KAAK;gBACH,OAAO,8BAA8B;YACvC,KAAK;gBACH,OAAO,8BAA8B;QACzC;QACA,IAAI,aAAa,OAAO,MACtB,OAAQ,KAAK,QAAQ;YACnB,KAAK;gBACH,OAAO,AAAC,OAAO,6BAA6B,KAAK,MAAM,EAAE,CAAC,IAAK;YACjE,KAAK;gBACH,OAAO,qCAAqC,KAAK,IAAI;YACvD,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,qCAAqC,KAAK;gBACnD,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aAAa,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK;QACzD,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IACE,aAAa,OAAO,QACpB,eAAe,OAAO,QACtB,SAAS,uBACT,SAAS,uBACT,SAAS,0BACT,SAAS,uBACT,SAAS,4BACT,SAAS,wBACR,aAAa,OAAO,QACnB,SAAS,QACT,CAAC,KAAK,QAAQ,KAAK,mBACjB,KAAK,QAAQ,KAAK,mBAClB,KAAK,QAAQ,KAAK,sBAClB,KAAK,QAAQ,KAAK,uBAClB,KAAK,QAAQ,KAAK,0BAClB,KAAK,QAAQ,KAAK,4BAClB,KAAK,MAAM,KAAK,WAAW,GAC/B;YACA,IAAI,WAAW,OAAO,QAAQ;YAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;gBACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB,EAAE;gBAChD,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;YACjC,OACE,QAAQ,KAAK,CACX;iBAED,kBAAkB,UAAU;QACrC,OAAO;YACL,WAAW;YACX,IACE,KAAK,MAAM,QACV,aAAa,OAAO,QACnB,SAAS,QACT,MAAM,OAAO,IAAI,CAAC,MAAM,MAAM,EAEhC,YACE;YACJ,SAAS,OACJ,mBAAmB,SACpB,YAAY,QACT,mBAAmB,UACpB,KAAK,MAAM,QAAQ,KAAK,QAAQ,KAAK,qBACnC,CAAC,AAAC,mBACA,MACA,CAAC,yBAAyB,KAAK,IAAI,KAAK,SAAS,IACjD,OACD,WACC,oEAAqE,IACtE,mBAAmB,OAAO;YACnC,QAAQ,KAAK,CACX,2IACA,kBACA;QAEJ;QACA,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aAAa,MAAM,UAAU,MAAM,QAAQ,YAAY;IAChE;IACA,SAAS,kBAAkB,IAAI,EAAE,UAAU;QACzC,IACE,aAAa,OAAO,QACpB,QACA,KAAK,QAAQ,KAAK,wBAElB;YAAA,IAAI,YAAY,OACd,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;gBACpC,IAAI,QAAQ,IAAI,CAAC,EAAE;gBACnB,eAAe,UAAU,oBAAoB,OAAO;YACtD;iBACG,IAAI,eAAe,OACtB,KAAK,MAAM,IAAI,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;iBACtC,IACF,SAAS,QAAQ,aAAa,OAAO,OACjC,IAAI,OACL,CAAC,AAAC,IACA,AAAC,yBAAyB,IAAI,CAAC,sBAAsB,IACrD,IAAI,CAAC,aAAa,EACnB,IAAI,eAAe,OAAO,IAAI,IAAI,IAAK,GAC5C,eAAe,OAAO,KACpB,MAAM,KAAK,OAAO,IAClB,CAAC,AAAC,IAAI,EAAE,IAAI,CAAC,OAAQ,MAAM,IAAI,GAEjC,MAAO,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE,IAAI,EAC5B,eAAe,KAAK,KAAK,KACvB,oBAAoB,KAAK,KAAK,EAAE;QAAW;IACrD;IACA,SAAS,eAAe,MAAM;QAC5B,OACE,aAAa,OAAO,UACpB,SAAS,UACT,OAAO,QAAQ,KAAK;IAExB;IACA,SAAS,oBAAoB,OAAO,EAAE,UAAU;QAC9C,IACE,QAAQ,MAAM,IACd,CAAC,QAAQ,MAAM,CAAC,SAAS,IACzB,QAAQ,QAAQ,GAAG,IACnB,CAAC,AAAC,QAAQ,MAAM,CAAC,SAAS,GAAG,GAC5B,aAAa,6BAA6B,aAC3C,CAAC,qBAAqB,CAAC,WAAW,GAClC;YACA,qBAAqB,CAAC,WAAW,GAAG,CAAC;YACrC,IAAI,aAAa;YACjB,WACE,QAAQ,QAAQ,MAAM,IACtB,QAAQ,MAAM,KAAK,cACnB,CAAC,AAAC,aAAa,MACf,aAAa,OAAO,QAAQ,MAAM,CAAC,GAAG,GACjC,aAAa,yBAAyB,QAAQ,MAAM,CAAC,IAAI,IAC1D,aAAa,OAAO,QAAQ,MAAM,CAAC,IAAI,IACvC,CAAC,aAAa,QAAQ,MAAM,CAAC,IAAI,GACpC,aAAa,iCAAiC,aAAa,GAAI;YAClE,IAAI,sBAAsB,qBAAqB,eAAe;YAC9D,qBAAqB,eAAe,GAAG;gBACrC,IAAI,QAAQ,qCAAqC,QAAQ,IAAI;gBAC7D,uBAAuB,CAAC,SAAS,yBAAyB,EAAE;gBAC5D,OAAO;YACT;YACA,QAAQ,KAAK,CACX,2HACA,YACA;YAEF,qBAAqB,eAAe,GAAG;QACzC;IACF;IACA,SAAS,6BAA6B,UAAU;QAC9C,IAAI,OAAO,IACT,QAAQ;QACV,SACE,CAAC,QAAQ,yBAAyB,MAAM,IAAI,CAAC,KAC7C,CAAC,OAAO,qCAAqC,QAAQ,IAAI;QAC3D,QACG,CAAC,aAAa,yBAAyB,WAAW,KACjD,CAAC,OACC,gDAAgD,aAAa,IAAI;QACvE,OAAO;IACT;IACA,IAAI,qHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,uBAAuB,OAAO,GAAG,CAAC,oBAClC,wBAAwB,OAAO,QAAQ,EACvC,2BAA2B,OAAO,GAAG,CAAC,2BACtC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,SAAS,OAAO,MAAM,EACtB,2BAA2B,OAAO,GAAG,CAAC,2BACtC,cAAc,MAAM,OAAO,EAC3B,gBAAgB,GAChB,SACA,UACA,UACA,WACA,WACA,oBACA;IACF,YAAY,kBAAkB,GAAG,CAAC;IAClC,IAAI,QACF,QACA,UAAU,CAAC;IACb,IAAI,sBAAsB,IAAI,CAC5B,eAAe,OAAO,UAAU,UAAU,GAC5C;IACA,IAAI,yBAAyB,OAAO,GAAG,CAAC,2BACtC;IACF,IAAI,yBAAyB,CAAC;IAC9B,IAAI,wBAAwB,CAAC,GAC3B,wBAAwB,CAAC;IAC3B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,OAAO,WAAW,MAAM,QAAQ,UAAU,kBAAkB,QAAQ;IACtE;AACF", "ignoreList": [0]}}, {"offset": {"line": 682, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 687, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/Reactprojects/landingpage-blocksconnect/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0]}}, {"offset": {"line": 694, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}